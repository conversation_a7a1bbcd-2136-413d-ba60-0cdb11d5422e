# 🌐 CORS Setup for IIS Server

## 📋 Files Created/Modified

### 1. `web.config` (IIS Configuration)
```xml
<httpProtocol>
  <customHeaders>
    <add name="Access-Control-Allow-Origin" value="*" />
    <add name="Access-Control-Allow-Methods" value="GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD" />
    <add name="Access-Control-Allow-Headers" value="Content-Type, Authorization, X-Requested-With, Accept, Origin, X-CSRF-TOKEN" />
    <add name="Access-Control-Max-Age" value="3600" />
  </customHeaders>
</httpProtocol>

<!-- Handle OPTIONS requests -->
<rule name="Handle OPTIONS" stopProcessing="true">
  <match url=".*" />
  <conditions>
    <add input="{REQUEST_METHOD}" pattern="OPTIONS" ignoreCase="true" />
  </conditions>
  <action type="CustomResponse" statusCode="200" statusReason="OK" statusDescription="OK" />
</rule>
```

### 2. `.htaccess` (Apache Fallback)
```apache
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept, Origin, X-CSRF-TOKEN"
    Header always set Access-Control-Max-Age "3600"
</IfModule>
```

### 3. Laravel Middleware (`ForceCorsHeaders.php`)
```php
public function handle(Request $request, Closure $next)
{
    if ($request->getMethod() === "OPTIONS") {
        return response('', 200)
            ->header('Access-Control-Allow-Origin', '*')
            ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD')
            ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin, X-CSRF-TOKEN')
            ->header('Access-Control-Max-Age', '3600');
    }

    $response = $next($request);
    
    // Force CORS headers
    $response->headers->set('Access-Control-Allow-Origin', '*');
    $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD');
    $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin, X-CSRF-TOKEN');
    
    return $response;
}
```

## 🧪 Test Endpoints

### 1. Simple Test
```bash
curl -H "Origin: https://miniapp.nitropardazesh.site" https://provider.nitropardazesh.site/api/cors-test-simple
```

### 2. OPTIONS Test
```bash
curl -X OPTIONS -H "Origin: https://miniapp.nitropardazesh.site" -H "Access-Control-Request-Method: GET" https://provider.nitropardazesh.site/api/plans
```

### 3. JavaScript Test (from browser console)
```javascript
fetch('https://provider.nitropardazesh.site/api/cors-test-simple', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
  }
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));
```

## 🔧 Troubleshooting

### If CORS still doesn't work:

1. **Check IIS Modules:**
   - Make sure URL Rewrite Module is installed
   - Verify HTTP Response Headers module is enabled

2. **IIS Manager Settings:**
   - Go to your site in IIS Manager
   - Open "HTTP Response Headers"
   - Add the CORS headers manually:
     - `Access-Control-Allow-Origin: *`
     - `Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD`
     - `Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin, X-CSRF-TOKEN`

3. **Alternative web.config (if above doesn't work):**
```xml
<system.webServer>
  <httpProtocol>
    <customHeaders>
      <clear />
      <add name="Access-Control-Allow-Origin" value="*" />
      <add name="Access-Control-Allow-Methods" value="*" />
      <add name="Access-Control-Allow-Headers" value="*" />
    </customHeaders>
  </httpProtocol>
</system.webServer>
```

4. **Check Laravel Routes:**
   - Make sure `api/` routes are working
   - Test: `https://provider.nitropardazesh.site/api/health`

## 📱 Next.js Usage

After CORS is fixed, use this in your Next.js app:

```javascript
// Simple fetch
const response = await fetch('https://provider.nitropardazesh.site/api/nextjs/telegram-auth', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    telegram_id: telegramUser.id,
    first_name: telegramUser.first_name,
    last_name: telegramUser.last_name,
    username: telegramUser.username,
    agent_id: agentId
  })
});

const result = await response.json();
if (result.success) {
  localStorage.setItem('auth_token', result.data.token);
  // User is authenticated!
}
```

## 🚀 Expected Headers

After setup, your API responses should include:
```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD
Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin, X-CSRF-TOKEN
Access-Control-Max-Age: 3600
```

## 📞 Support

If CORS still doesn't work:
1. Check browser developer tools Network tab
2. Look for preflight OPTIONS requests
3. Verify response headers
4. Check IIS error logs
5. Test with curl commands above
