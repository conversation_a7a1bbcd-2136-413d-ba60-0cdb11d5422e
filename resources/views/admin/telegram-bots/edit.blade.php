@extends('layouts.admin')

@section('title', 'ویرایش ربات تلگرام')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">🤖 ویرایش ربات تلگرام</h1>
                <a href="{{ route('admin.telegram-bots.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> بازگشت به لیست
                </a>
            </div>
        </div>
    </div>

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">⚙️ ویرایش اطلاعات ربات</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.telegram-bots.update', $telegramBot) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="mb-3">
                            <label class="form-label">نماینده</label>
                            <div class="form-control-plaintext">
                                <strong>{{ $telegramBot->agent->user->name }}</strong>
                                ({{ $telegramBot->agent->user->email }}) - کد: {{ $telegramBot->agent->referral_code }}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="bot_token" class="form-label">توکن ربات <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="bot_token" name="bot_token" 
                                   value="{{ old('bot_token', $telegramBot->bot_token) }}" 
                                   placeholder="1234567890:ABCdefGHIjklMNOpqrsTUVwxyz" required>
                            <div class="form-text">
                                توکن ربات را از @BotFather در تلگرام دریافت کنید.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="bot_username" class="form-label">نام کاربری ربات <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">@</span>
                                <input type="text" class="form-control" id="bot_username" name="bot_username" 
                                       value="{{ old('bot_username', $telegramBot->bot_username) }}" 
                                       placeholder="my_agent_bot" required>
                            </div>
                            <div class="form-text">
                                نام کاربری ربات باید با نام کاربری تنظیم شده در BotFather مطابقت داشته باشد.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="welcome_message" class="form-label">پیام خوشامدگویی</label>
                            <textarea class="form-control" id="welcome_message" name="welcome_message" rows="5" 
                                      placeholder="پیام خوشامدگویی سفارشی...">{{ old('welcome_message', $telegramBot->welcome_message) }}</textarea>
                            <div class="form-text">
                                پیام خوشامدگویی که هنگام ارسال دستور /start به کاربر نمایش داده می‌شود. در صورت خالی بودن، پیام پیش‌فرض نمایش داده می‌شود.
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" role="switch" id="is_active" name="is_active" 
                                       {{ old('is_active', $telegramBot->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    ربات فعال باشد
                                </label>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ route('admin.telegram-bots.index') }}" class="btn btn-outline-secondary">
                                انصراف
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> ذخیره تغییرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">📊 اطلاعات ربات</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>وضعیت فعلی:</strong><br>
                        <span class="badge bg-{{ $telegramBot->is_active ? 'success' : 'warning' }}">
                            {{ $telegramBot->is_active ? 'فعال' : 'غیرفعال' }}
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Webhook:</strong><br>
                        <span class="badge bg-{{ $telegramBot->hasWebhook() ? 'success' : 'danger' }}">
                            {{ $telegramBot->hasWebhook() ? 'تنظیم شده' : 'تنظیم نشده' }}
                        </span>
                    </div>
                    
                    @if($telegramBot->webhook_set_at)
                        <div class="mb-3">
                            <strong>آخرین تنظیم Webhook:</strong><br>
                            <small>{{ $telegramBot->webhook_set_at->format('Y/m/d H:i') }}</small>
                        </div>
                    @endif
                    
                    <div class="mb-3">
                        <strong>تعداد کاربران:</strong><br>
                        {{ $telegramBot->getTotalUsersCount() }} نفر
                    </div>
                    
                    <div class="mb-3">
                        <strong>کاربران فعال:</strong><br>
                        {{ $telegramBot->getActiveUsersCount() }} نفر
                    </div>
                    
                    <div class="mb-3">
                        <strong>کاربران ثبت‌نام شده:</strong><br>
                        {{ $telegramBot->getRegisteredUsersCount() }} نفر
                    </div>
                    
                    <div class="mb-3">
                        <strong>لینک ربات:</strong><br>
                        <a href="https://t.me/{{ $telegramBot->bot_username }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fab fa-telegram"></i> باز کردن در تلگرام
                        </a>
                    </div>

                    <hr>

                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="testBot()">
                            <i class="fas fa-vial"></i> تست ربات
                        </button>
                        @if(app()->environment('local'))
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="simulateWebhook()">
                                <i class="fas fa-play"></i> شبیه‌سازی Webhook
                            </button>
                        @endif
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="resetWebhook()">
                            <i class="fas fa-sync"></i> تنظیم مجدد Webhook
                        </button>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">⚠️ نکات مهم</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <ul class="mb-0">
                            <li>تغییر توکن ربات باعث تنظیم مجدد webhook می‌شود.</li>
                            <li>غیرفعال کردن ربات باعث توقف پاسخگویی به کاربران می‌شود.</li>
                            <li>تغییر پیام خوشامدگویی فقط برای کاربران جدید اعمال می‌شود.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function testBot() {
    fetch('{{ route("admin.telegram-bots.test", $telegramBot) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطا در تست ربات');
    });
}

function simulateWebhook() {
    fetch('{{ url("api/telegram/webhook/{$telegramBot->id}/simulate") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ شبیه‌سازی webhook با موفقیت انجام شد!\n\nپیام /start از کاربر تست ارسال شد.');
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطا در شبیه‌سازی webhook');
    });
}

function resetWebhook() {
    if (!confirm('آیا از تنظیم مجدد webhook اطمینان دارید؟')) {
        return;
    }

    fetch('{{ route("admin.telegram-bots.reset-webhook", $telegramBot) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
            location.reload();
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطا در تنظیم مجدد webhook');
    });
}
</script>
@endpush
