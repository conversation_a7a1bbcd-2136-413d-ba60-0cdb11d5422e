<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Agent;
use App\Models\TelegramUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class NextJsAuthController extends Controller
{
    public function __construct()
    {
        // Add CORS headers for all methods
        $this->middleware(function ($request, $next) {
            $response = $next($request);
            
            $response->headers->set('Access-Control-Allow-Origin', 'https://miniapp.nitropardazesh.site');
            $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
            $response->headers->set('Access-Control-Allow-Credentials', 'true');
            
            return $response;
        });
    }

    /**
     * Handle OPTIONS requests for CORS
     */
    public function options()
    {
        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * Auto register/login user from Telegram WebApp
     */
    public function telegramAuth(Request $request)
    {
        try {
            Log::info('Telegram auth request received', $request->all());

            // Validate required fields
            $request->validate([
                'id' => 'required|integer',
                'first_name' => 'required|string',
                'agent_id' => 'required|integer|exists:agents,id'
            ]);

            $telegramData = [
                'id' => $request->input('id'),
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
                'username' => $request->input('username'),
                'photo_url' => $request->input('photo_url'),
            ];

            $agent = Agent::find($request->input('agent_id'));

            // Find telegram bot for this agent
            $telegramBot = $agent->telegramBot;
            if (!$telegramBot) {
                return response()->json([
                    'success' => false,
                    'message' => 'Telegram bot not configured for this agent',
                    'error_code' => 'BOT_NOT_CONFIGURED'
                ], 400);
            }

            // Find or create telegram user
            $telegramUser = TelegramUser::where('telegram_user_id', $telegramData['id'])
                ->where('telegram_bot_id', $telegramBot->id)
                ->first();

            if (!$telegramUser) {
                $telegramUser = TelegramUser::create([
                    'telegram_bot_id' => $telegramBot->id,
                    'telegram_user_id' => $telegramData['id'],
                    'telegram_username' => $telegramData['username'],
                    'first_name' => $telegramData['first_name'],
                    'last_name' => $telegramData['last_name'],
                    'last_interaction' => now(),
                ]);
            } else {
                // Update user info
                $telegramUser->update([
                    'telegram_username' => $telegramData['username'],
                    'first_name' => $telegramData['first_name'],
                    'last_name' => $telegramData['last_name'],
                    'last_interaction' => now(),
                ]);
            }

            // Check if user is already registered
            if ($telegramUser->is_registered && $telegramUser->user) {
                $user = $telegramUser->user;
                $token = $user->createToken('nextjs-auth')->plainTextToken;
                
                return response()->json([
                    'success' => true,
                    'message' => 'User logged in successfully',
                    'data' => [
                        'user' => [
                            'id' => $user->id,
                            'name' => $user->name,
                            'email' => $user->email,
                            'telegram_id' => $user->telegram_id,
                            'telegram_username' => $user->telegram_username,
                            'profile_photo_url' => $user->profile_photo_url,
                        ],
                        'agent' => [
                            'id' => $agent->id,
                            'name' => $agent->user->name,
                            'referral_code' => $agent->referral_code,
                        ],
                        'token' => $token,
                        'is_new_user' => false
                    ]
                ]);
            }

            // Auto register new user
            $userData = $this->createUserFromTelegram($telegramData, $agent);
            $user = User::create($userData);

            // Link telegram user to website user
            $telegramUser->update([
                'is_registered' => true,
                'user_id' => $user->id,
            ]);

            // Update user with agent reference
            $user->update(['referred_by_agent_id' => $agent->id]);

            // Generate token
            $token = $user->createToken('nextjs-auth')->plainTextToken;

            Log::info('Auto registration completed', [
                'user_id' => $user->id,
                'telegram_user_id' => $telegramData['id'],
                'agent_id' => $agent->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'User registered and logged in successfully',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'telegram_id' => $user->telegram_id,
                        'telegram_username' => $user->telegram_username,
                        'profile_photo_url' => $user->profile_photo_url,
                    ],
                    'agent' => [
                        'id' => $agent->id,
                        'name' => $agent->user->name,
                        'referral_code' => $agent->referral_code,
                    ],
                    'token' => $token,
                    'is_new_user' => true
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
                'error_code' => 'VALIDATION_ERROR'
            ], 422);

        } catch (\Exception $e) {
            Log::error('Telegram auth error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Authentication failed',
                'error_code' => 'AUTH_ERROR'
            ], 500);
        }
    }

    /**
     * Get user profile
     */
    public function profile(Request $request)
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized',
                'error_code' => 'UNAUTHORIZED'
            ], 401);
        }

        $agent = null;
        if ($user->referred_by_agent_id) {
            $agent = Agent::find($user->referred_by_agent_id);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'telegram_id' => $user->telegram_id,
                    'telegram_username' => $user->telegram_username,
                    'profile_photo_url' => $user->profile_photo_url,
                ],
                'agent' => $agent ? [
                    'id' => $agent->id,
                    'name' => $agent->user->name,
                    'referral_code' => $agent->referral_code,
                ] : null
            ]
        ]);
    }

    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        $user = $request->user();
        
        if ($user) {
            $user->currentAccessToken()->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    }

    /**
     * Create user data from Telegram info
     */
    private function createUserFromTelegram(array $telegramData, Agent $agent): array
    {
        $firstName = $telegramData['first_name'];
        $lastName = $telegramData['last_name'] ?? '';
        $username = $telegramData['username'] ?? null;
        
        // Generate email
        $email = $username 
            ? $username . '@telegram.local'
            : 'user_' . $telegramData['id'] . '@telegram.local';

        // Generate a secure random password
        $password = Str::random(16);

        return [
            'name' => trim($firstName . ' ' . $lastName),
            'email' => $email,
            'password' => Hash::make($password),
            'email_verified_at' => now(),
            'telegram_id' => $telegramData['id'],
            'telegram_username' => $username,
            'profile_photo_url' => $telegramData['photo_url'] ?? null,
        ];
    }
}
