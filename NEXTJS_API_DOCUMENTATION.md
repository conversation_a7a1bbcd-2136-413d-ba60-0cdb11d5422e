# 🚀 API Documentation for Next.js Frontend

## Base URL
```
https://provider.nitropardazesh.site/api
```

## Frontend URL
```
https://miniapp.nitropardazesh.site
```

---

## 🔐 Authentication Endpoints

### 1. Telegram Auto Authentication
**Endpoint:** `POST /nextjs/telegram-auth`

**Description:** Automatically register/login user using Telegram WebApp data

**Request Body:**
```json
{
  "id": 123456789,
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "username": "johndo<PERSON>",
  "photo_url": "https://t.me/i/userpic/320/johndoe.jpg",
  "agent_id": 1
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "User registered and logged in successfully",
  "data": {
    "user": {
      "id": 1,
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "telegram_id": 123456789,
      "telegram_username": "johndo<PERSON>",
      "profile_photo_url": "https://t.me/i/userpic/320/johndoe.jpg"
    },
    "agent": {
      "id": 1,
      "name": "Agent Name",
      "referral_code": "AGT001"
    },
    "token": "1|abcdef123456...",
    "is_new_user": true
  }
}
```

**Error Response (400):**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "id": ["The id field is required."],
    "agent_id": ["The selected agent id is invalid."]
  },
  "error_code": "VALIDATION_ERROR"
}
```

---

### 2. Get User Profile
**Endpoint:** `GET /nextjs/profile`

**Headers:**
```
Authorization: Bearer {token}
```

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "telegram_id": 123456789,
      "telegram_username": "johndoe",
      "profile_photo_url": "https://t.me/i/userpic/320/johndoe.jpg"
    },
    "agent": {
      "id": 1,
      "name": "Agent Name",
      "referral_code": "AGT001"
    }
  }
}
```

---

### 3. Logout
**Endpoint:** `POST /nextjs/logout`

**Headers:**
```
Authorization: Bearer {token}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

---

## 🎯 Usage in Next.js

### 1. Install Dependencies
```bash
npm install axios
```

### 2. Create API Client
```javascript
// lib/api.js
import axios from 'axios';

const API_BASE_URL = 'https://provider.nitropardazesh.site/api';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Add token to requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default apiClient;
```

### 3. Telegram Authentication Hook
```javascript
// hooks/useTelegramAuth.js
import { useState, useEffect } from 'react';
import apiClient from '../lib/api';

export const useTelegramAuth = (agentId) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const authenticateUser = async () => {
      try {
        // Get Telegram WebApp data
        const tg = window.Telegram?.WebApp;
        if (!tg) {
          throw new Error('Telegram WebApp not available');
        }

        const user = tg.initDataUnsafe?.user;
        if (!user) {
          throw new Error('User data not available');
        }

        // Send auth request
        const response = await apiClient.post('/nextjs/telegram-auth', {
          id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          username: user.username,
          photo_url: user.photo_url,
          agent_id: agentId,
        });

        if (response.data.success) {
          const { user: userData, token } = response.data.data;
          
          // Store token
          localStorage.setItem('auth_token', token);
          
          // Set user data
          setUser(userData);
        }
      } catch (err) {
        setError(err.response?.data?.message || err.message);
      } finally {
        setLoading(false);
      }
    };

    if (agentId) {
      authenticateUser();
    }
  }, [agentId]);

  return { user, loading, error };
};
```

### 4. Main App Component
```javascript
// pages/index.js
import { useRouter } from 'next/router';
import { useTelegramAuth } from '../hooks/useTelegramAuth';
import { useEffect } from 'react';

export default function Home() {
  const router = useRouter();
  const { agent_id } = router.query;
  const { user, loading, error } = useTelegramAuth(agent_id);

  useEffect(() => {
    // Initialize Telegram WebApp
    if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
      const tg = window.Telegram.WebApp;
      tg.ready();
      tg.expand();
    }
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4">در حال ورود...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center text-red-500">
          <p>خطا: {error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded"
          >
            تلاش مجدد
          </button>
        </div>
      </div>
    );
  }

  if (user) {
    return (
      <div className="p-4">
        <h1>خوش آمدید {user.name}!</h1>
        <p>شما با موفقیت وارد شدید.</p>
        {/* Your app content here */}
      </div>
    );
  }

  return null;
}
```

### 5. Add Telegram WebApp Script
```javascript
// pages/_document.js
import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="fa" dir="rtl">
      <Head>
        <script src="https://telegram.org/js/telegram-web-app.js"></script>
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
```

---

## 🔧 Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Request validation failed |
| `AGENT_NOT_FOUND` | Agent ID not found |
| `BOT_NOT_CONFIGURED` | Telegram bot not configured for agent |
| `AUTH_ERROR` | General authentication error |
| `UNAUTHORIZED` | Token invalid or expired |

---

## 🌐 CORS Configuration

The API is configured to accept requests from:
- `https://miniapp.nitropardazesh.site`

Make sure your Next.js app is deployed on this domain for the API to work properly.

---

## 📱 Telegram WebApp Integration

### URL Parameters:
- `agent_id`: Required parameter to identify the agent

### Example URLs:
- Main app: `https://miniapp.nitropardazesh.site?agent_id=1`
- Plans page: `https://miniapp.nitropardazesh.site/plans?agent_id=1`
- Register page: `https://miniapp.nitropardazesh.site/register?agent_id=1`

---

## 🚀 Deployment Notes

1. Make sure your Next.js app is deployed on `https://miniapp.nitropardazesh.site`
2. The Laravel backend should be on `https://provider.nitropardazesh.site`
3. CORS is configured to allow requests between these domains
4. Use HTTPS for both frontend and backend
5. Test the Telegram WebApp integration thoroughly

---

## 📞 Support

If you encounter any issues with the API, check the Laravel logs:
```bash
tail -f storage/logs/laravel.log
```
