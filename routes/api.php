<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\PlanController;
use App\Http\Controllers\Api\PurchaseController;
use App\Http\Controllers\Api\PaymentController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Plans (public access)
Route::get('/plans', [PlanController::class, 'index']);
Route::get('/plans/{plan}', [PlanController::class, 'show']);

// Payment routes (public for callback)
Route::post('/payment/callback', [PaymentController::class, 'callback']);
Route::get('/payment/status', [PaymentController::class, 'status']);
Route::get('/payment/test', [PaymentController::class, 'test']); // Test endpoint (debug only)

// Telegram webhook routes (public for Telegram API)
Route::post('/telegram/webhook/{botId}', [App\Http\Controllers\Api\TelegramWebhookController::class, 'webhook']);
Route::get('/telegram/webhook/{botId}/test', [App\Http\Controllers\Api\TelegramWebhookController::class, 'test']);
Route::post('/telegram/webhook/{botId}/set', [App\Http\Controllers\Api\TelegramWebhookController::class, 'setWebhook']);
Route::post('/telegram/webhook/{botId}/remove', [App\Http\Controllers\Api\TelegramWebhookController::class, 'removeWebhook']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/profile', [AuthController::class, 'profile']);
    Route::put('/profile', [AuthController::class, 'updateProfile']);

    // Purchase routes
    Route::get('/purchases', [PurchaseController::class, 'index']); // All purchases history
    Route::post('/purchases', [PurchaseController::class, 'store']);
    Route::get('/purchases/{purchase}', [PurchaseController::class, 'show']);
    Route::get('/my-active-plans', [PurchaseController::class, 'myActivePlans']); // Only active plans

    // Admin/Agent plan routes
    Route::get('/admin/plans', [PlanController::class, 'all']); // All plans with filters
    Route::get('/plans/{plan}/statistics', [PlanController::class, 'statistics']); // Plan statistics
});

// Health check
Route::get('/health', function () {
    return response()->json([
        'success' => true,
        'message' => 'API is working',
        'timestamp' => now()
    ]);
});
