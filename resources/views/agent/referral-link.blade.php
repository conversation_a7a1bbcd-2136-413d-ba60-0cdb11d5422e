<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لینک معرف من</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .referral-code { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px; 
            padding: 30px; 
            text-align: center; 
            font-size: 2em; 
            font-weight: bold;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .link-box {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 20px;
            font-family: monospace;
            font-size: 1.1em;
            word-break: break-all;
        }
        .copy-btn {
            transition: all 0.3s;
        }
        .copy-btn:hover {
            transform: translateY(-2px);
        }
        .share-btn {
            border-radius: 50px;
            padding: 10px 20px;
            margin: 5px;
            transition: all 0.3s;
        }
        .share-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="#">پنل نماینده</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('agent.dashboard') }}">داشبورد</a>
                <a class="nav-link" href="{{ route('agent.sales.index') }}">فروش‌ها</a>
                <a class="nav-link" href="{{ route('agent.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link" href="{{ route('agent.withdrawals.index') }}">تسویه حساب</a>
                <a class="nav-link active" href="{{ route('agent.referral-link') }}">لینک معرف</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="text-center mb-5">
            <h1>🔗 لینک معرف شما</h1>
            <p class="text-muted">این لینک را با دوستان و مشتریان خود به اشتراک بگذارید</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- کد معرف -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="referral-code">
                            {{ $agent->referral_code }}
                        </div>
                        <p class="text-center mt-3 mb-0">کد معرف شما</p>
                    </div>
                </div>

                <!-- لینک کامل -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>🌐 لینک کامل معرف</h5>
                    </div>
                    <div class="card-body">
                        <div class="link-box" id="referralLink">
                            {{ $referralLink }}
                        </div>
                        <div class="text-center mt-3">
                            <button class="btn btn-primary copy-btn" onclick="copyLink()">
                                📋 کپی لینک
                            </button>
                        </div>
                    </div>
                </div>

                <!-- راه‌های اشتراک‌گذاری -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>📤 اشتراک‌گذاری</h5>
                    </div>
                    <div class="card-body text-center">
                        <p class="mb-3">لینک خود را در شبکه‌های اجتماعی به اشتراک بگذارید:</p>
                        
                        <button class="btn btn-success share-btn" onclick="shareWhatsApp()">
                            📱 واتساپ
                        </button>
                        <button class="btn btn-info share-btn" onclick="shareTelegram()">
                            ✈️ تلگرام
                        </button>
                        <button class="btn btn-primary share-btn" onclick="shareTwitter()">
                            🐦 توییتر
                        </button>
                        <button class="btn btn-secondary share-btn" onclick="shareEmail()">
                            📧 ایمیل
                        </button>
                    </div>
                </div>

                <!-- آمار معرفی -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>📊 آمار معرفی‌های شما</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h3 class="text-primary">{{ $agent->purchases()->distinct('user_id')->count() }}</h3>
                                <small>کل مشتریان</small>
                            </div>
                            <div class="col-md-3">
                                <h3 class="text-success">{{ number_format($agent->total_sales) }}</h3>
                                <small>کل فروش (تومان)</small>
                            </div>
                            <div class="col-md-3">
                                <h3 class="text-warning">{{ number_format($agent->total_commission) }}</h3>
                                <small>کل کمیسیون (تومان)</small>
                            </div>
                            <div class="col-md-3">
                                <h3 class="text-info">{{ $agent->commission_rate }}%</h3>
                                <small>درصد کمیسیون</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- راهنمای استفاده -->
                <div class="card">
                    <div class="card-header">
                        <h5>💡 راهنمای استفاده</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">🔹 لینک معرف خود را با دوستان به اشتراک بگذارید</li>
                            <li class="mb-2">🔹 هر خریدی که از طریق لینک شما انجام شود، کمیسیون دریافت می‌کنید</li>
                            <li class="mb-2">🔹 کمیسیون شما {{ $agent->commission_rate }}% از مبلغ خرید است</li>
                            <li class="mb-2">🔹 کمیسیون‌ها به کیف پول شما اضافه می‌شود</li>
                            <li class="mb-2">🔹 می‌توانید درخواست تسویه حساب دهید</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const referralLink = "{{ $referralLink }}";
        const referralCode = "{{ $agent->referral_code }}";

        function copyLink() {
            navigator.clipboard.writeText(referralLink).then(function() {
                alert('لینک کپی شد!');
            });
        }

        function shareWhatsApp() {
            const text = `سلام! 👋\n\nبا استفاده از لینک زیر می‌توانید از پلن‌های ویژه ما استفاده کنید:\n\n${referralLink}\n\nکد معرف: ${referralCode}`;
            window.open(`https://wa.me/?text=${encodeURIComponent(text)}`);
        }

        function shareTelegram() {
            const text = `سلام! 👋\n\nبا استفاده از لینک زیر می‌توانید از پلن‌های ویژه ما استفاده کنید:\n\n${referralLink}\n\nکد معرف: ${referralCode}`;
            window.open(`https://t.me/share/url?url=${encodeURIComponent(referralLink)}&text=${encodeURIComponent(text)}`);
        }

        function shareTwitter() {
            const text = `پلن‌های ویژه را از اینجا بررسی کنید! کد معرف: ${referralCode}`;
            window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(referralLink)}`);
        }

        function shareEmail() {
            const subject = 'دعوت به استفاده از پلن‌های ویژه';
            const body = `سلام،\n\nبا استفاده از لینک زیر می‌توانید از پلن‌های ویژه ما استفاده کنید:\n\n${referralLink}\n\nکد معرف: ${referralCode}\n\nبا تشکر`;
            window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
        }
    </script>
</body>
</html>
