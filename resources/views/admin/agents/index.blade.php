<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدیریت نمایندگان</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .stats-card { border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">پنل مدیریت</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">داشبورد</a>
                <a class="nav-link active" href="{{ route('admin.agents.index') }}">نمایندگان</a>
                <a class="nav-link" href="{{ route('admin.plans.index') }}">پلن‌ها</a>
                <a class="nav-link" href="{{ route('admin.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link" href="{{ route('admin.withdrawals.index') }}">درخواست‌های تسویه</a>
            </div>
            <form method="POST" action="{{ route('logout') }}" class="d-inline">
                @csrf
                <button type="submit" class="btn btn-outline-light btn-sm">خروج</button>
            </form>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>مدیریت نمایندگان</h1>
            <a href="{{ route('admin.agents.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> افزودن نماینده جدید
            </a>
        </div>

        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <!-- فیلترها -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="جستجو در نام، ایمیل یا کد معرف" value="{{ request('search') }}">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">همه وضعیت‌ها</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>فعال</option>
                            <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>غیرفعال</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary">فیلتر</button>
                        <a href="{{ route('admin.agents.index') }}" class="btn btn-outline-secondary">پاک کردن</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- لیست نمایندگان -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>نماینده</th>
                                <th>کد معرف</th>
                                <th>درصد کمیسیون</th>
                                <th>کل فروش</th>
                                <th>کل کمیسیون</th>
                                <th>موجودی کیف پول</th>
                                <th>وضعیت</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($agents as $agent)
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ $agent->user->name }}</strong><br>
                                        <small class="text-muted">{{ $agent->user->email }}</small><br>
                                        @if($agent->user->phone)
                                            <small class="text-muted">{{ $agent->user->phone }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ $agent->referral_code }}</span>
                                </td>
                                <td>{{ $agent->commission_rate }}%</td>
                                <td>{{ number_format($agent->total_sales) }} تومان</td>
                                <td>{{ number_format($agent->total_commission) }} تومان</td>
                                <td>{{ number_format($agent->wallet_balance) }} تومان</td>
                                <td>
                                    <span class="badge bg-{{ $agent->is_active ? 'success' : 'danger' }}">
                                        {{ $agent->is_active ? 'فعال' : 'غیرفعال' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.agents.show', $agent) }}" class="btn btn-sm btn-outline-primary">
                                            مشاهده
                                        </a>
                                        <a href="{{ route('admin.agents.edit', $agent) }}" class="btn btn-sm btn-outline-warning">
                                            ویرایش
                                        </a>
                                        <form method="POST" action="{{ route('admin.agents.destroy', $agent) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                    onclick="return confirm('آیا از حذف این نماینده اطمینان دارید؟')">
                                                حذف
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center">هیچ نماینده‌ای یافت نشد</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                {{ $agents->links() }}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
