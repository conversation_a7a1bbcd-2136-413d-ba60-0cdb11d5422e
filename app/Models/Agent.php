<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Agent extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'referral_code',
        'commission_rate',
        'total_sales',
        'total_commission',
        'wallet_balance',
        'bank_account_number',
        'bank_account_name',
        'bank_name',
        'crypto_wallet_type',
        'crypto_wallet_address',
        'crypto_network',
        'is_active',
    ];

    protected $casts = [
        'commission_rate' => 'decimal:2',
        'total_sales' => 'decimal:2',
        'total_commission' => 'decimal:2',
        'wallet_balance' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($agent) {
            if (empty($agent->referral_code)) {
                $agent->referral_code = $agent->generateUniqueReferralCode();
            }
        });
    }

    /**
     * Generate unique referral code
     */
    private function generateUniqueReferralCode(): string
    {
        do {
            $code = strtoupper(Str::random(8));
        } while (self::where('referral_code', $code)->exists());
        
        return $code;
    }

    /**
     * Get the user that owns this agent profile
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get purchases made through this agent's referral
     */
    public function purchases()
    {
        return $this->hasMany(Purchase::class);
    }

    /**
     * Get commissions earned by this agent
     */
    public function commissions()
    {
        return $this->hasMany(Commission::class);
    }

    /**
     * Get transactions for this agent
     */
    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Get users referred by this agent
     */
    public function referredUsers()
    {
        return $this->hasMany(User::class, 'referred_by_agent_id');
    }

    /**
     * Calculate total commission for a specific period
     */
    public function getCommissionForPeriod($startDate, $endDate)
    {
        return $this->commissions()
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');
    }

    /**
     * Get monthly sales statistics
     */
    public function getMonthlySales($year, $month)
    {
        return $this->purchases()
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->sum('amount');
    }

    /**
     * Get withdrawal requests for this agent
     */
    public function withdrawalRequests()
    {
        return $this->hasMany(\App\Models\WithdrawalRequest::class);
    }

    /**
     * Check if agent has crypto wallet configured
     */
    public function hasCryptoWallet(): bool
    {
        return !empty($this->crypto_wallet_type) &&
               !empty($this->crypto_wallet_address) &&
               !empty($this->crypto_network);
    }

    /**
     * Check if agent has bank account configured
     */
    public function hasBankAccount(): bool
    {
        return !empty($this->bank_account_number) &&
               !empty($this->bank_account_name) &&
               !empty($this->bank_name);
    }

    /**
     * Get minimum withdrawal amount
     */
    public function getMinWithdrawalAmount(): float
    {
        return 50000; // 50,000 تومان
    }

    /**
     * Check if agent can withdraw specified amount
     */
    public function canWithdraw(float $amount): bool
    {
        return $this->wallet_balance >= $amount &&
               $amount >= $this->getMinWithdrawalAmount();
    }
}
