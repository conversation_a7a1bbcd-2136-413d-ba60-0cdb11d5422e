<?php

namespace App\Services;

use App\Models\TelegramBot;
use App\Models\TelegramUser;
use App\Models\Plan;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TelegramBotService
{
    private $bot;

    public function __construct(TelegramBot $bot = null)
    {
        $this->bot = $bot;
    }

    /**
     * Set the bot instance
     */
    public function setBot(TelegramBot $bot)
    {
        $this->bot = $bot;
        return $this;
    }

    /**
     * Send message to telegram user
     */
    public function sendMessage(int $chatId, string $text, array $options = [])
    {
        if (!$this->bot) {
            throw new \Exception('Bot instance not set');
        }

        $url = $this->bot->getBotApiUrl() . '/sendMessage';
        
        $data = array_merge([
            'chat_id' => $chatId,
            'text' => $text,
            'parse_mode' => 'HTML',
        ], $options);

        try {
            $response = Http::timeout(30)->post($url, $data);
            
            if ($response->successful()) {
                return $response->json();
            } else {
                Log::error('Telegram API error', [
                    'bot_id' => $this->bot->id,
                    'response' => $response->body(),
                    'status' => $response->status()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Telegram send message error', [
                'bot_id' => $this->bot->id,
                'error' => $e->getMessage(),
                'chat_id' => $chatId
            ]);
            return false;
        }
    }

    /**
     * Set webhook for the bot
     */
    public function setWebhook(): bool
    {
        if (!$this->bot) {
            throw new \Exception('Bot instance not set');
        }

        $url = $this->bot->getBotApiUrl() . '/setWebhook';
        $webhookUrl = $this->bot->getWebhookUrl();

        try {
            $response = Http::timeout(30)->post($url, [
                'url' => $webhookUrl,
                'allowed_updates' => ['message', 'callback_query'],
            ]);

            if ($response->successful()) {
                $this->bot->update([
                    'webhook_url' => $webhookUrl,
                    'webhook_set_at' => now(),
                ]);
                
                Log::info('Webhook set successfully', [
                    'bot_id' => $this->bot->id,
                    'webhook_url' => $webhookUrl
                ]);
                
                return true;
            } else {
                Log::error('Failed to set webhook', [
                    'bot_id' => $this->bot->id,
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Webhook setup error', [
                'bot_id' => $this->bot->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Remove webhook
     */
    public function removeWebhook(): bool
    {
        if (!$this->bot) {
            throw new \Exception('Bot instance not set');
        }

        $url = $this->bot->getBotApiUrl() . '/deleteWebhook';

        try {
            $response = Http::timeout(30)->post($url);

            if ($response->successful()) {
                $this->bot->update([
                    'webhook_url' => null,
                    'webhook_set_at' => null,
                ]);
                return true;
            }
            return false;
        } catch (\Exception $e) {
            Log::error('Remove webhook error', [
                'bot_id' => $this->bot->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get bot info
     */
    public function getBotInfo()
    {
        if (!$this->bot) {
            throw new \Exception('Bot instance not set');
        }

        $url = $this->bot->getBotApiUrl() . '/getMe';

        try {
            $response = Http::timeout(30)->get($url);
            
            if ($response->successful()) {
                return $response->json();
            }
            return false;
        } catch (\Exception $e) {
            Log::error('Get bot info error', [
                'bot_id' => $this->bot->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Process incoming webhook update
     */
    public function processUpdate(array $update)
    {
        if (!$this->bot) {
            throw new \Exception('Bot instance not set');
        }

        Log::info('Processing telegram update', [
            'bot_id' => $this->bot->id,
            'update' => $update
        ]);

        // Handle message
        if (isset($update['message'])) {
            return $this->handleMessage($update['message']);
        }

        // Handle callback query
        if (isset($update['callback_query'])) {
            return $this->handleCallbackQuery($update['callback_query']);
        }

        return false;
    }

    /**
     * Handle incoming message
     */
    private function handleMessage(array $message)
    {
        $chatId = $message['chat']['id'];
        $text = $message['text'] ?? '';
        $from = $message['from'];

        // Get or create telegram user
        $telegramUser = $this->getOrCreateTelegramUser($from, $chatId);
        
        // Update last interaction
        $telegramUser->updateLastInteraction();

        // Handle commands
        if (str_starts_with($text, '/')) {
            return $this->handleCommand($text, $chatId, $telegramUser);
        }

        // Handle regular text
        return $this->handleText($text, $chatId, $telegramUser);
    }

    /**
     * Get or create telegram user
     */
    private function getOrCreateTelegramUser(array $from, int $chatId): TelegramUser
    {
        return TelegramUser::updateOrCreate(
            [
                'telegram_bot_id' => $this->bot->id,
                'telegram_user_id' => $from['id'],
            ],
            [
                'telegram_username' => $from['username'] ?? null,
                'first_name' => $from['first_name'] ?? null,
                'last_name' => $from['last_name'] ?? null,
                'last_interaction' => now(),
            ]
        );
    }

    /**
     * Handle bot commands
     */
    private function handleCommand(string $command, int $chatId, TelegramUser $telegramUser)
    {
        $command = strtolower(trim($command));

        switch ($command) {
            case '/start':
                return $this->handleStartCommand($chatId, $telegramUser);
            
            case '/help':
                return $this->handleHelpCommand($chatId, $telegramUser);
            
            case '/plans':
                return $this->handlePlansCommand($chatId, $telegramUser);
            
            case '/register':
                return $this->handleRegisterCommand($chatId, $telegramUser);
            
            case '/status':
                return $this->handleStatusCommand($chatId, $telegramUser);
            
            default:
                return $this->sendMessage($chatId, 
                    "دستور نامعتبر! ❌\n\nبرای مشاهده دستورات موجود /help را ارسال کنید."
                );
        }
    }

    /**
     * Handle regular text messages
     */
    private function handleText(string $text, int $chatId, TelegramUser $telegramUser)
    {
        // For now, just send a help message
        return $this->sendMessage($chatId,
            "سلام! 👋\n\nبرای استفاده از ربات، از دستورات زیر استفاده کنید:\n\n" .
            "🔹 /help - راهنما\n" .
            "🔹 /plans - مشاهده پلن‌ها\n" .
            "🔹 /register - ثبت‌نام در سایت"
        );
    }

    /**
     * Handle /start command
     */
    private function handleStartCommand(int $chatId, TelegramUser $telegramUser)
    {
        $welcomeMessage = $this->bot->getWelcomeMessage();

        // Add user statistics to welcome message
        $agent = $this->bot->agent;
        $stats = $agent->getTelegramStats();

        $message = $welcomeMessage . "\n\n";
        $message .= "📊 آمار ربات:\n";
        $message .= "👥 تعداد کاربران: {$stats['total_users']}\n";
        $message .= "✅ کاربران فعال: {$stats['active_users']}\n";
        $message .= "🎯 ثبت‌نام شده: {$stats['registered_users']}";

        return $this->sendMessage($chatId, $message);
    }

    /**
     * Handle /help command
     */
    private function handleHelpCommand(int $chatId, TelegramUser $telegramUser)
    {
        $message = "📋 راهنمای استفاده از ربات:\n\n";
        $message .= "🔹 /start - شروع مجدد و نمایش پیام خوشامدگویی\n";
        $message .= "🔹 /help - نمایش این راهنما\n";
        $message .= "🔹 /plans - مشاهده پلن‌های موجود\n";
        $message .= "🔹 /register - ثبت‌نام در سایت\n";
        $message .= "🔹 /status - وضعیت حساب شما\n\n";

        $agent = $this->bot->agent;
        $message .= "👤 نماینده: {$agent->user->name}\n";
        $message .= "🎯 کد معرف: {$agent->referral_code}\n\n";
        $message .= "💬 برای پشتیبانی با نماینده تماس بگیرید.";

        return $this->sendMessage($chatId, $message);
    }

    /**
     * Handle /plans command
     */
    private function handlePlansCommand(int $chatId, TelegramUser $telegramUser)
    {
        $plans = Plan::where('status', 'active')->orderBy('sort_order')->orderBy('price')->get();

        if ($plans->isEmpty()) {
            return $this->sendMessage($chatId, "در حال حاضر پلنی موجود نیست. ❌");
        }

        $message = "📦 پلن‌های موجود:\n\n";

        foreach ($plans as $plan) {
            $message .= "🔸 {$plan->name}\n";
            if ($plan->type) {
                $message .= "📂 نوع: {$plan->type}\n";
            }
            $message .= "💰 قیمت: " . number_format($plan->price) . " تومان\n";
            $message .= "⏱ مدت: {$plan->formatted_duration}\n";
            $message .= "📝 توضیحات: {$plan->description}\n";

            if ($plan->features) {
                $features = is_array($plan->features) ? $plan->features : json_decode($plan->features, true);
                if ($features) {
                    $message .= "✨ ویژگی‌ها:\n";
                    foreach ($features as $feature) {
                        $message .= "  • {$feature}\n";
                    }
                }
            }
            $message .= "\n";
        }

        $agent = $this->bot->agent;
        $message .= "🛒 برای خرید پلن‌ها:\n";
        $message .= "1️⃣ از دکمه زیر وارد فروشگاه شوید\n";
        $message .= "2️⃣ کد معرف شما: {$agent->referral_code}\n\n";
        $message .= "💡 با استفاده از کد معرف، از تخفیف‌های ویژه بهره‌مند شوید!";

        // Create inline keyboard for miniapp
        $keyboard = [
            'inline_keyboard' => [
                [
                    [
                        'text' => '🛒 ورود به فروشگاه',
                        'web_app' => ['url' => url("/plans?ref={$agent->referral_code}")]
                    ]
                ],
                [
                    [
                        'text' => '📞 تماس با نماینده',
                        'url' => "tg://user?id={$agent->user->phone}"
                    ]
                ]
            ]
        ];

        return $this->sendMessage($chatId, $message, [
            'reply_markup' => json_encode($keyboard)
        ]);
    }

    /**
     * Handle /register command
     */
    private function handleRegisterCommand(int $chatId, TelegramUser $telegramUser)
    {
        $agent = $this->bot->agent;

        if ($telegramUser->is_registered) {
            $message = "شما قبلاً در سایت ثبت‌نام کرده‌اید! ✅\n\n";
            $message .= "👤 نام: {$telegramUser->user->name}\n";
            $message .= "📧 ایمیل: {$telegramUser->user->email}\n\n";
            $message .= "🛒 برای خرید پلن‌ها از دکمه زیر استفاده کنید:";

            $keyboard = [
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🛒 ورود به فروشگاه',
                            'web_app' => ['url' => url("/plans?ref={$agent->referral_code}")]
                        ]
                    ]
                ]
            ];

            return $this->sendMessage($chatId, $message, [
                'reply_markup' => json_encode($keyboard)
            ]);
        } else {
            $message = "🎯 ثبت‌نام در سایت:\n\n";
            $message .= "برای استفاده از پلن‌ها و خرید، ابتدا در سایت ثبت‌نام کنید.\n\n";
            $message .= "✨ مزایای ثبت‌نام:\n";
            $message .= "• دسترسی به تمام پلن‌ها\n";
            $message .= "• پشتیبانی اختصاصی نماینده\n";
            $message .= "• تخفیف‌های ویژه\n";
            $message .= "• پیگیری سفارشات\n";
            $message .= "• دسترسی به پنل کاربری\n\n";
            $message .= "🎁 کد معرف شما: {$agent->referral_code}";

            $keyboard = [
                'inline_keyboard' => [
                    [
                        [
                            'text' => '📝 ثبت‌نام در سایت',
                            'web_app' => ['url' => url("/register?ref={$agent->referral_code}")]
                        ]
                    ],
                    [
                        [
                            'text' => '🛒 مشاهده پلن‌ها',
                            'web_app' => ['url' => url("/plans?ref={$agent->referral_code}")]
                        ]
                    ]
                ]
            ];

            return $this->sendMessage($chatId, $message, [
                'reply_markup' => json_encode($keyboard)
            ]);
        }
    }

    /**
     * Handle /status command
     */
    private function handleStatusCommand(int $chatId, TelegramUser $telegramUser)
    {
        $message = "📊 وضعیت حساب شما:\n\n";
        $message .= "👤 نام: {$telegramUser->getFullName()}\n";
        $message .= "🆔 شناسه تلگرام: {$telegramUser->telegram_user_id}\n";

        if ($telegramUser->telegram_username) {
            $message .= "📱 نام کاربری: @{$telegramUser->telegram_username}\n";
        }

        $message .= "📅 عضویت: " . $telegramUser->created_at->format('Y/m/d H:i') . "\n";
        $message .= "🕐 آخرین فعالیت: " . $telegramUser->last_interaction->format('Y/m/d H:i') . "\n";

        if ($telegramUser->is_registered) {
            $message .= "✅ وضعیت: ثبت‌نام شده در سایت\n";
            $message .= "📧 ایمیل: {$telegramUser->user->email}\n";

            // Show purchase history
            $purchases = $telegramUser->user->purchases()->latest()->limit(3)->get();
            if ($purchases->count() > 0) {
                $message .= "\n🛒 آخرین خریدها:\n";
                foreach ($purchases as $purchase) {
                    $status = $purchase->status === 'completed' ? '✅' : '⏳';
                    $message .= "{$status} {$purchase->plan->name} - " .
                               number_format($purchase->amount) . " تومان\n";
                }
            }
        } else {
            $message .= "❌ وضعیت: ثبت‌نام نشده\n";
            $message .= "\n💡 برای ثبت‌نام از دستور /register استفاده کنید.";
        }

        $agent = $this->bot->agent;
        $message .= "\n\n👤 نماینده شما: {$agent->user->name}";
        $message .= "\n🎯 کد معرف: {$agent->referral_code}";

        return $this->sendMessage($chatId, $message);
    }

    /**
     * Handle callback query
     */
    private function handleCallbackQuery(array $callbackQuery)
    {
        // Implementation for inline keyboard callbacks
        // This can be extended based on specific needs
        return true;
    }
}
