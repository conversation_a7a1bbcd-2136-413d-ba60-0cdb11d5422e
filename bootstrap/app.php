<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Add global CORS middleware to API routes - no restrictions
        $middleware->api(prepend: [
            \App\Http\Middleware\NoRestrictionsCors::class,
        ]);

        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
            'agent' => \App\Http\Middleware\AgentMiddleware::class,
            'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
            'nextjs.cors' => \App\Http\Middleware\NextJsCors::class,
            'open.cors' => \App\Http\Middleware\OpenCors::class,
            'no.cors' => \App\Http\Middleware\NoRestrictionsCors::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
