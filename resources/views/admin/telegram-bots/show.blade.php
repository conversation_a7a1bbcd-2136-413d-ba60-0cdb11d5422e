@extends('layouts.admin')

@section('title', 'جزئیات ربات تلگرام')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">🤖 جزئیات ربات تلگرام</h1>
                <div>
                    <a href="{{ route('admin.telegram-bots.edit', $telegramBot) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> ویرایش
                    </a>
                    <a href="{{ route('admin.telegram-bots.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> بازگشت به لیست
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- اطلاعات ربات -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">📋 اطلاعات ربات</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>نام کاربری:</strong><br>
                        <code>@{{ $telegramBot->bot_username }}</code>
                    </div>
                    
                    <div class="mb-3">
                        <strong>وضعیت:</strong><br>
                        <span class="badge bg-{{ $telegramBot->is_active ? 'success' : 'warning' }} fs-6">
                            {{ $telegramBot->is_active ? 'فعال' : 'غیرفعال' }}
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Webhook:</strong><br>
                        <span class="badge bg-{{ $telegramBot->hasWebhook() ? 'success' : 'danger' }} fs-6">
                            {{ $telegramBot->hasWebhook() ? 'تنظیم شده' : 'تنظیم نشده' }}
                        </span>
                    </div>
                    
                    @if($telegramBot->webhook_set_at)
                        <div class="mb-3">
                            <strong>آخرین تنظیم Webhook:</strong><br>
                            <small>{{ $telegramBot->webhook_set_at->format('Y/m/d H:i') }}</small>
                        </div>
                    @endif
                    
                    <div class="mb-3">
                        <strong>تاریخ ایجاد:</strong><br>
                        {{ $telegramBot->created_at->format('Y/m/d H:i') }}
                    </div>
                    
                    <div class="mb-3">
                        <strong>آخرین بروزرسانی:</strong><br>
                        {{ $telegramBot->updated_at->format('Y/m/d H:i') }}
                    </div>
                    
                    <div class="mb-3">
                        <strong>لینک ربات:</strong><br>
                        <a href="https://t.me/{{ $telegramBot->bot_username }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fab fa-telegram"></i> باز کردن در تلگرام
                        </a>
                    </div>

                    <hr>

                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="testBot()">
                            <i class="fas fa-vial"></i> تست ربات
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="resetWebhook()">
                            <i class="fas fa-sync"></i> تنظیم مجدد Webhook
                        </button>
                        <button type="button" class="btn btn-outline-{{ $telegramBot->is_active ? 'warning' : 'success' }} btn-sm" 
                                onclick="toggleBotStatus()">
                            <i class="fas fa-power-off"></i> 
                            {{ $telegramBot->is_active ? 'غیرفعال کردن' : 'فعال کردن' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- اطلاعات نماینده -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">👤 اطلاعات نماینده</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>نام:</strong><br>
                        {{ $telegramBot->agent->user->name }}
                    </div>
                    
                    <div class="mb-3">
                        <strong>ایمیل:</strong><br>
                        {{ $telegramBot->agent->user->email }}
                    </div>
                    
                    @if($telegramBot->agent->user->phone)
                        <div class="mb-3">
                            <strong>تلفن:</strong><br>
                            {{ $telegramBot->agent->user->phone }}
                        </div>
                    @endif
                    
                    <div class="mb-3">
                        <strong>کد معرف:</strong><br>
                        <span class="badge bg-info fs-6">{{ $telegramBot->agent->referral_code }}</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>درصد کمیسیون:</strong><br>
                        {{ $telegramBot->agent->commission_rate }}%
                    </div>
                    
                    <div class="mb-3">
                        <strong>کل فروش:</strong><br>
                        {{ number_format($telegramBot->agent->total_sales) }} تومان
                    </div>
                    
                    <div class="mb-3">
                        <strong>کل کمیسیون:</strong><br>
                        {{ number_format($telegramBot->agent->total_commission) }} تومان
                    </div>

                    <hr>

                    <a href="{{ route('admin.agents.show', $telegramBot->agent) }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> مشاهده جزئیات نماینده
                    </a>
                </div>
            </div>
        </div>

        <!-- آمار کاربران -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">📊 آمار کاربران</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h3>{{ $stats['total_users'] }}</h3>
                                    <p class="mb-0">کل کاربران</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h4>{{ $stats['active_users'] }}</h4>
                                    <p class="mb-0">فعال</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h4>{{ $stats['registered_users'] }}</h4>
                                    <p class="mb-0">ثبت‌نام شده</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- پیام خوشامدگویی -->
    @if($telegramBot->welcome_message)
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">💬 پیام خوشامدگویی</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-light border">
                        {!! nl2br(e($telegramBot->welcome_message)) !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- لیست کاربران اخیر -->
    @if($telegramBot->telegramUsers->count() > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">👥 آخرین کاربران</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>کاربر</th>
                                    <th>شناسه تلگرام</th>
                                    <th>وضعیت</th>
                                    <th>آخرین فعالیت</th>
                                    <th>تاریخ عضویت</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($telegramBot->telegramUsers()->latest('last_interaction')->limit(10)->get() as $user)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $user->getFullName() }}</strong>
                                            @if($user->telegram_username)
                                                <br><small class="text-muted">@{{ $user->telegram_username }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>{{ $user->telegram_user_id }}</td>
                                    <td>
                                        @if($user->is_registered)
                                            <span class="badge bg-success">ثبت‌نام شده</span>
                                        @else
                                            <span class="badge bg-secondary">ثبت‌نام نشده</span>
                                        @endif
                                        
                                        @if($user->isActive())
                                            <span class="badge bg-info">فعال</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($user->last_interaction)
                                            {{ $user->last_interaction->format('Y/m/d H:i') }}
                                            <br>
                                            <small class="text-muted">{{ $user->last_interaction->diffForHumans() }}</small>
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>
                                        {{ $user->created_at->format('Y/m/d H:i') }}
                                        <br>
                                        <small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    @if($telegramBot->telegramUsers->count() > 10)
                        <div class="text-center mt-3">
                            <small class="text-muted">و {{ $telegramBot->telegramUsers->count() - 10 }} کاربر دیگر...</small>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
function testBot() {
    fetch('{{ route("admin.telegram-bots.test", $telegramBot) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطا در تست ربات');
    });
}

function resetWebhook() {
    if (!confirm('آیا از تنظیم مجدد webhook اطمینان دارید؟')) {
        return;
    }
    
    fetch('{{ route("admin.telegram-bots.reset-webhook", $telegramBot) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
            location.reload();
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطا در تنظیم مجدد webhook');
    });
}

function toggleBotStatus() {
    if (!confirm('آیا از تغییر وضعیت ربات اطمینان دارید؟')) {
        return;
    }

    fetch('{{ route("admin.telegram-bots.toggle-status", $telegramBot) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
            location.reload();
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطا در تغییر وضعیت ربات');
    });
}
</script>
@endpush
